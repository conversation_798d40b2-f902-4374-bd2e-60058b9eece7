import QuickResponseIcon from '@/components/Global/Icons/QuickResponseIcon';
import { useHumanHandoverStore } from '@/stores/humanHandover/humanHandoverStore';
import { Menu, MenuButton, MenuItem, MenuItems } from '@headlessui/react';

const MenuQuickResponse = ({ handleQuickResponseClick = () => {} }) => {
  const { quickResponses } = useHumanHandoverStore((state) => state);

  return (
    <div className="relative w-full flex items-center justify-center">
      <Menu>
        <MenuButton className="data-[open]:text-purple-300 data-[open]:bg-white">
          <QuickResponseIcon />
        </MenuButton>
        <MenuItems
          className="absolute left-1/2 top-1/2 transform -translate-y-[6%]
                     !max-w-[450px] w-[400px] border border-purple-50 rounded-size1 
                     bg-white flex flex-col gap-size1 p-size1 z-50"
          anchor="top end"
        >
          {quickResponses.length > 0 ? quickResponses.map((quickResponse) => (
            <MenuItem key={quickResponse.id}>
              <button
                className="flex flex-col gap-size1 p-size1 w-full text-left hover:bg-gray-50 rounded-size1"
                onClick={() => handleQuickResponseClick(quickResponse.content)}
              >
                <h1 className="text-sm font-medium">{quickResponse.title}</h1>
                <p className="text-sm text-gray-500">
                  {quickResponse.content}
                </p>
              </button>
            </MenuItem>
          )) : (
              <p className="text-sm text-gray-500">No quick responses found</p>
          )}
        </MenuItems>
      </Menu>
    </div>
  );
};

export default MenuQuickResponse;